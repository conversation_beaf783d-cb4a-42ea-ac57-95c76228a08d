
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "C:/msys64/mingw64/share/cmake/Modules/"
    found: "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/msys64/mingw64/bin/g++.exe 
      Build flags: -static
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        C:/work/code/java/data-annotation-platform/native/build_static_opencv/CMakeFiles/4.1.0/CompilerIdCXX/a.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/ar"
      - "C:/msys64/mingw64/bin/ar.com"
    found: "C:/msys64/mingw64/bin/ar.exe"
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/ranlib"
      - "C:/msys64/mingw64/bin/ranlib.com"
    found: "C:/msys64/mingw64/bin/ranlib.exe"
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/strip"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/strip.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/strip.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/strip"
      - "C:/msys64/mingw64/bin/strip.com"
    found: "C:/msys64/mingw64/bin/strip.exe"
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/ld"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/ld.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/ld.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/ld"
      - "C:/msys64/mingw64/bin/ld.com"
    found: "C:/msys64/mingw64/bin/ld.exe"
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/nm"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/nm.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/nm.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/nm"
      - "C:/msys64/mingw64/bin/nm.com"
    found: "C:/msys64/mingw64/bin/nm.exe"
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/objdump"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/objdump.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/objdump.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/objdump"
      - "C:/msys64/mingw64/bin/objdump.com"
    found: "C:/msys64/mingw64/bin/objdump.exe"
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/objcopy"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/objcopy.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/objcopy.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/objcopy"
      - "C:/msys64/mingw64/bin/objcopy.com"
    found: "C:/msys64/mingw64/bin/objcopy.exe"
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/readelf"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/readelf.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/readelf.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/readelf"
      - "C:/msys64/mingw64/bin/readelf.com"
    found: "C:/msys64/mingw64/bin/readelf.exe"
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/dlltool"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/dlltool.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/dlltool.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/dlltool"
      - "C:/msys64/mingw64/bin/dlltool.com"
    found: "C:/msys64/mingw64/bin/dlltool.exe"
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/addr2line"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/addr2line.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/addr2line.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/addr2line"
      - "C:/msys64/mingw64/bin/addr2line.com"
    found: "C:/msys64/mingw64/bin/addr2line.exe"
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:206 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/tapi"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/tapi.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/tapi.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/tapi"
      - "C:/msys64/mingw64/bin/tapi.com"
      - "C:/msys64/mingw64/bin/tapi.exe"
      - "C:/msys64/mingw64/bin/tapi"
      - "C:/msys64/usr/local/bin/tapi.com"
      - "C:/msys64/usr/local/bin/tapi.exe"
      - "C:/msys64/usr/local/bin/tapi"
      - "C:/msys64/usr/bin/tapi.com"
      - "C:/msys64/usr/bin/tapi.exe"
      - "C:/msys64/usr/bin/tapi"
      - "C:/Windows/System32/tapi.com"
      - "C:/Windows/System32/tapi.exe"
      - "C:/Windows/System32/tapi"
      - "C:/Windows/tapi.com"
      - "C:/Windows/tapi.exe"
      - "C:/Windows/tapi"
      - "C:/Windows/System32/wbem/tapi.com"
      - "C:/Windows/System32/wbem/tapi.exe"
      - "C:/Windows/System32/wbem/tapi"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi"
      - "C:/msys64/usr/bin/site_perl/tapi.com"
      - "C:/msys64/usr/bin/site_perl/tapi.exe"
      - "C:/msys64/usr/bin/site_perl/tapi"
      - "C:/msys64/usr/bin/vendor_perl/tapi.com"
      - "C:/msys64/usr/bin/vendor_perl/tapi.exe"
      - "C:/msys64/usr/bin/vendor_perl/tapi"
      - "C:/msys64/usr/bin/core_perl/tapi.com"
      - "C:/msys64/usr/bin/core_perl/tapi.exe"
      - "C:/msys64/usr/bin/core_perl/tapi"
    found: false
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-15.1"
      - "gcc-ar-15"
      - "gcc-ar15"
      - "gcc-ar"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ar-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ar-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ar-15.1"
      - "C:/msys64/mingw64/bin/gcc-ar-15.1.com"
      - "C:/msys64/mingw64/bin/gcc-ar-15.1.exe"
      - "C:/msys64/mingw64/bin/gcc-ar-15.1"
      - "C:/msys64/usr/local/bin/gcc-ar-15.1.com"
      - "C:/msys64/usr/local/bin/gcc-ar-15.1.exe"
      - "C:/msys64/usr/local/bin/gcc-ar-15.1"
      - "C:/msys64/usr/bin/gcc-ar-15.1.com"
      - "C:/msys64/usr/bin/gcc-ar-15.1.exe"
      - "C:/msys64/usr/bin/gcc-ar-15.1"
      - "C:/Windows/System32/gcc-ar-15.1.com"
      - "C:/Windows/System32/gcc-ar-15.1.exe"
      - "C:/Windows/System32/gcc-ar-15.1"
      - "C:/Windows/gcc-ar-15.1.com"
      - "C:/Windows/gcc-ar-15.1.exe"
      - "C:/Windows/gcc-ar-15.1"
      - "C:/Windows/System32/wbem/gcc-ar-15.1.com"
      - "C:/Windows/System32/wbem/gcc-ar-15.1.exe"
      - "C:/Windows/System32/wbem/gcc-ar-15.1"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1"
      - "C:/msys64/usr/bin/site_perl/gcc-ar-15.1.com"
      - "C:/msys64/usr/bin/site_perl/gcc-ar-15.1.exe"
      - "C:/msys64/usr/bin/site_perl/gcc-ar-15.1"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ar-15.1.com"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ar-15.1.exe"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ar-15.1"
      - "C:/msys64/usr/bin/core_perl/gcc-ar-15.1.com"
      - "C:/msys64/usr/bin/core_perl/gcc-ar-15.1.exe"
      - "C:/msys64/usr/bin/core_perl/gcc-ar-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ar-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ar-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ar-15"
      - "C:/msys64/mingw64/bin/gcc-ar-15.com"
      - "C:/msys64/mingw64/bin/gcc-ar-15.exe"
      - "C:/msys64/mingw64/bin/gcc-ar-15"
      - "C:/msys64/usr/local/bin/gcc-ar-15.com"
      - "C:/msys64/usr/local/bin/gcc-ar-15.exe"
      - "C:/msys64/usr/local/bin/gcc-ar-15"
      - "C:/msys64/usr/bin/gcc-ar-15.com"
      - "C:/msys64/usr/bin/gcc-ar-15.exe"
      - "C:/msys64/usr/bin/gcc-ar-15"
      - "C:/Windows/System32/gcc-ar-15.com"
      - "C:/Windows/System32/gcc-ar-15.exe"
      - "C:/Windows/System32/gcc-ar-15"
      - "C:/Windows/gcc-ar-15.com"
      - "C:/Windows/gcc-ar-15.exe"
      - "C:/Windows/gcc-ar-15"
      - "C:/Windows/System32/wbem/gcc-ar-15.com"
      - "C:/Windows/System32/wbem/gcc-ar-15.exe"
      - "C:/Windows/System32/wbem/gcc-ar-15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15"
      - "C:/msys64/usr/bin/site_perl/gcc-ar-15.com"
      - "C:/msys64/usr/bin/site_perl/gcc-ar-15.exe"
      - "C:/msys64/usr/bin/site_perl/gcc-ar-15"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ar-15.com"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ar-15.exe"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ar-15"
      - "C:/msys64/usr/bin/core_perl/gcc-ar-15.com"
      - "C:/msys64/usr/bin/core_perl/gcc-ar-15.exe"
      - "C:/msys64/usr/bin/core_perl/gcc-ar-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ar15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ar15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ar15"
      - "C:/msys64/mingw64/bin/gcc-ar15.com"
      - "C:/msys64/mingw64/bin/gcc-ar15.exe"
      - "C:/msys64/mingw64/bin/gcc-ar15"
      - "C:/msys64/usr/local/bin/gcc-ar15.com"
      - "C:/msys64/usr/local/bin/gcc-ar15.exe"
      - "C:/msys64/usr/local/bin/gcc-ar15"
      - "C:/msys64/usr/bin/gcc-ar15.com"
      - "C:/msys64/usr/bin/gcc-ar15.exe"
      - "C:/msys64/usr/bin/gcc-ar15"
      - "C:/Windows/System32/gcc-ar15.com"
      - "C:/Windows/System32/gcc-ar15.exe"
      - "C:/Windows/System32/gcc-ar15"
      - "C:/Windows/gcc-ar15.com"
      - "C:/Windows/gcc-ar15.exe"
      - "C:/Windows/gcc-ar15"
      - "C:/Windows/System32/wbem/gcc-ar15.com"
      - "C:/Windows/System32/wbem/gcc-ar15.exe"
      - "C:/Windows/System32/wbem/gcc-ar15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15"
      - "C:/msys64/usr/bin/site_perl/gcc-ar15.com"
      - "C:/msys64/usr/bin/site_perl/gcc-ar15.exe"
      - "C:/msys64/usr/bin/site_perl/gcc-ar15"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ar15.com"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ar15.exe"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ar15"
      - "C:/msys64/usr/bin/core_perl/gcc-ar15.com"
      - "C:/msys64/usr/bin/core_perl/gcc-ar15.exe"
      - "C:/msys64/usr/bin/core_perl/gcc-ar15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ar"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ar.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ar.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ar"
      - "C:/msys64/mingw64/bin/gcc-ar.com"
    found: "C:/msys64/mingw64/bin/gcc-ar.exe"
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-15.1"
      - "gcc-ranlib-15"
      - "gcc-ranlib15"
      - "gcc-ranlib"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ranlib-15.1.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ranlib-15.1.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ranlib-15.1"
      - "C:/msys64/mingw64/bin/gcc-ranlib-15.1.com"
      - "C:/msys64/mingw64/bin/gcc-ranlib-15.1.exe"
      - "C:/msys64/mingw64/bin/gcc-ranlib-15.1"
      - "C:/msys64/usr/local/bin/gcc-ranlib-15.1.com"
      - "C:/msys64/usr/local/bin/gcc-ranlib-15.1.exe"
      - "C:/msys64/usr/local/bin/gcc-ranlib-15.1"
      - "C:/msys64/usr/bin/gcc-ranlib-15.1.com"
      - "C:/msys64/usr/bin/gcc-ranlib-15.1.exe"
      - "C:/msys64/usr/bin/gcc-ranlib-15.1"
      - "C:/Windows/System32/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/gcc-ranlib-15.1"
      - "C:/Windows/gcc-ranlib-15.1.com"
      - "C:/Windows/gcc-ranlib-15.1.exe"
      - "C:/Windows/gcc-ranlib-15.1"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.1"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1"
      - "C:/msys64/usr/bin/site_perl/gcc-ranlib-15.1.com"
      - "C:/msys64/usr/bin/site_perl/gcc-ranlib-15.1.exe"
      - "C:/msys64/usr/bin/site_perl/gcc-ranlib-15.1"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ranlib-15.1.com"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ranlib-15.1.exe"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ranlib-15.1"
      - "C:/msys64/usr/bin/core_perl/gcc-ranlib-15.1.com"
      - "C:/msys64/usr/bin/core_perl/gcc-ranlib-15.1.exe"
      - "C:/msys64/usr/bin/core_perl/gcc-ranlib-15.1"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ranlib-15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ranlib-15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ranlib-15"
      - "C:/msys64/mingw64/bin/gcc-ranlib-15.com"
      - "C:/msys64/mingw64/bin/gcc-ranlib-15.exe"
      - "C:/msys64/mingw64/bin/gcc-ranlib-15"
      - "C:/msys64/usr/local/bin/gcc-ranlib-15.com"
      - "C:/msys64/usr/local/bin/gcc-ranlib-15.exe"
      - "C:/msys64/usr/local/bin/gcc-ranlib-15"
      - "C:/msys64/usr/bin/gcc-ranlib-15.com"
      - "C:/msys64/usr/bin/gcc-ranlib-15.exe"
      - "C:/msys64/usr/bin/gcc-ranlib-15"
      - "C:/Windows/System32/gcc-ranlib-15.com"
      - "C:/Windows/System32/gcc-ranlib-15.exe"
      - "C:/Windows/System32/gcc-ranlib-15"
      - "C:/Windows/gcc-ranlib-15.com"
      - "C:/Windows/gcc-ranlib-15.exe"
      - "C:/Windows/gcc-ranlib-15"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15"
      - "C:/msys64/usr/bin/site_perl/gcc-ranlib-15.com"
      - "C:/msys64/usr/bin/site_perl/gcc-ranlib-15.exe"
      - "C:/msys64/usr/bin/site_perl/gcc-ranlib-15"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ranlib-15.com"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ranlib-15.exe"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ranlib-15"
      - "C:/msys64/usr/bin/core_perl/gcc-ranlib-15.com"
      - "C:/msys64/usr/bin/core_perl/gcc-ranlib-15.exe"
      - "C:/msys64/usr/bin/core_perl/gcc-ranlib-15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ranlib15.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ranlib15.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ranlib15"
      - "C:/msys64/mingw64/bin/gcc-ranlib15.com"
      - "C:/msys64/mingw64/bin/gcc-ranlib15.exe"
      - "C:/msys64/mingw64/bin/gcc-ranlib15"
      - "C:/msys64/usr/local/bin/gcc-ranlib15.com"
      - "C:/msys64/usr/local/bin/gcc-ranlib15.exe"
      - "C:/msys64/usr/local/bin/gcc-ranlib15"
      - "C:/msys64/usr/bin/gcc-ranlib15.com"
      - "C:/msys64/usr/bin/gcc-ranlib15.exe"
      - "C:/msys64/usr/bin/gcc-ranlib15"
      - "C:/Windows/System32/gcc-ranlib15.com"
      - "C:/Windows/System32/gcc-ranlib15.exe"
      - "C:/Windows/System32/gcc-ranlib15"
      - "C:/Windows/gcc-ranlib15.com"
      - "C:/Windows/gcc-ranlib15.exe"
      - "C:/Windows/gcc-ranlib15"
      - "C:/Windows/System32/wbem/gcc-ranlib15.com"
      - "C:/Windows/System32/wbem/gcc-ranlib15.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15"
      - "C:/msys64/usr/bin/site_perl/gcc-ranlib15.com"
      - "C:/msys64/usr/bin/site_perl/gcc-ranlib15.exe"
      - "C:/msys64/usr/bin/site_perl/gcc-ranlib15"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ranlib15.com"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ranlib15.exe"
      - "C:/msys64/usr/bin/vendor_perl/gcc-ranlib15"
      - "C:/msys64/usr/bin/core_perl/gcc-ranlib15.com"
      - "C:/msys64/usr/bin/core_perl/gcc-ranlib15.exe"
      - "C:/msys64/usr/bin/core_perl/gcc-ranlib15"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/gcc-ranlib"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ranlib.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ranlib.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/gcc-ranlib"
      - "C:/msys64/mingw64/bin/gcc-ranlib.com"
    found: "C:/msys64/mingw64/bin/gcc-ranlib.exe"
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake:169 (enable_language)"
      - "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-CXX.cmake:2 (__windows_compiler_gnu)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXInformation.cmake:48 (include)"
      - "CMakeLists.txt:2 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "windres"
      - "windres"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/sbin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/mingw64/sbin/"
      - "C:/msys64/mingw64/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files (x86)/dataset_export/bin/"
      - "C:/Program Files (x86)/dataset_export/sbin/"
      - "C:/Program Files (x86)/dataset_export/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/sbin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/sbin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/sbin/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2/windres"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin/windres.com"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin/windres.exe"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin/windres"
      - "C:/msys64/mingw64/bin/windres.com"
    found: "C:/msys64/mingw64/bin/windres.exe"
    search_context:
      CMAKE_PROGRAM_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/bzip2"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/tools/curl/bin"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/dataset_export"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/msys64/mingw64"
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "C:/Program Files (x86)/dataset_export"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/work/code/java/data-annotation-platform/native/build_static_opencv/CMakeFiles/CMakeScratch/TryCompile-7rg2kn"
      binary: "C:/work/code/java/data-annotation-platform/native/build_static_opencv/CMakeFiles/CMakeScratch/TryCompile-7rg2kn"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-static"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "-static"
      VCPKG_INSTALLED_DIR: "C:/msys64/home/<USER>/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-mingw-static"
      Z_VCPKG_ROOT_DIR: "C:/msys64/home/<USER>/vcpkg"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/work/code/java/data-annotation-platform/native/build_static_opencv/CMakeFiles/CMakeScratch/TryCompile-7rg2kn'
        
        Run Build Command(s): C:/msys64/mingw64/bin/cmake.exe -E env VERBOSE=1 C:/msys64/mingw64/bin/mingw32-make.exe -f Makefile cmTC_11ea3/fast
        C:/msys64/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_11ea3.dir\\build.make CMakeFiles/cmTC_11ea3.dir/build
        mingw32-make[1]: Entering directory 'C:/work/code/java/data-annotation-platform/native/build_static_opencv/CMakeFiles/CMakeScratch/TryCompile-7rg2kn'
        Building CXX object CMakeFiles/cmTC_11ea3.dir/CMakeCXXCompilerABI.cpp.obj
        C:\\msys64\\mingw64\\bin\\g++.exe   -static    -v -o CMakeFiles\\cmTC_11ea3.dir\\CMakeCXXCompilerABI.cpp.obj -c C:\\msys64\\mingw64\\share\\cmake\\Modules\\CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=C:\\msys64\\mingw64\\bin\\g++.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-15.1.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.1.0 (Rev8, Built by MSYS2 project) 
        COLLECT_GCC_OPTIONS='-static' '-v' '-o' 'CMakeFiles\\cmTC_11ea3.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_11ea3.dir\\'
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/cc1plus.exe -quiet -v -iprefix C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -D_REENTRANT C:\\msys64\\mingw64\\share\\cmake\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_11ea3.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=nocona -version -o C:\\msys64\\tmp\\ccmzOlYf.s
        GNU C++17 (Rev8, Built by MSYS2 project) version 15.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.1.0, GMP version 6.3.0, MPFR version 4.2.2, MPC version 1.3.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring nonexistent directory "C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0"
        ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/x86_64-w64-mingw32"
        ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/backward"
        ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"
        ignoring nonexistent directory "D:/M/msys64/mingw64/include"
        ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"
        ignoring nonexistent directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "D:/M/msys64/mingw64/include"
        #include "..." search starts here:
        #include <...> search starts here:
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/x86_64-w64-mingw32
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/backward
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed
         /mingw64/include
        End of search list.
        Compiler executable checksum: 3b6fca90584e44f6819bdf9fe346ad35
        COLLECT_GCC_OPTIONS='-static' '-v' '-o' 'CMakeFiles\\cmTC_11ea3.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_11ea3.dir\\'
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_11ea3.dir\\CMakeCXXCompilerABI.cpp.obj C:\\msys64\\tmp\\ccmzOlYf.s
        GNU assembler version 2.45 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.45
        COMPILER_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-static' '-v' '-o' 'CMakeFiles\\cmTC_11ea3.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_11ea3.dir\\CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_11ea3.exe
        C:\\msys64\\mingw64\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_11ea3.dir\\link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=C:\\msys64\\mingw64\\bin\\g++.exe
        COLLECT_LTO_WRAPPER=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../gcc-15.1.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++
        Thread model: posix
        Supported LTO compression algorithms: zlib zstd
        gcc version 15.1.0 (Rev8, Built by MSYS2 project) 
        COMPILER_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;C:/msys64/mingw64/bin/../lib/gcc/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-static' '-static' '-v' '-o' 'cmTC_11ea3.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_11ea3.'
         C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\msys64\\tmp\\ccEeJD2q.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bstatic -o cmTC_11ea3.exe C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_11ea3.dir/objects.a --no-whole-archive --out-implib libcmTC_11ea3.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        collect2 version 15.1.0
        C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\msys64\\tmp\\ccEeJD2q.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bstatic -o cmTC_11ea3.exe C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_11ea3.dir/objects.a --no-whole-archive --out-implib libcmTC_11ea3.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        GNU ld (GNU Binutils) 2.45
        COLLECT_GCC_OPTIONS='-static' '-static' '-v' '-o' 'cmTC_11ea3.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_11ea3.'
        C:\\msys64\\mingw64\\bin\\cmake.exe -E rm -f CMakeFiles\\cmTC_11ea3.dir/objects.a
        C:\\msys64\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_11ea3.dir/objects.a @CMakeFiles\\cmTC_11ea3.dir\\objects1.rsp
        C:\\msys64\\mingw64\\bin\\g++.exe -static  -static -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_11ea3.dir/objects.a -Wl,--no-whole-archive -o cmTC_11ea3.exe -Wl,--out-implib,libcmTC_11ea3.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        mingw32-make[1]: Leaving directory 'C:/work/code/java/data-annotation-platform/native/build_static_opencv/CMakeFiles/CMakeScratch/TryCompile-7rg2kn'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0]
          add: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/x86_64-w64-mingw32]
          add: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/backward]
          add: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
          add: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include]
          add: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
          add: [/mingw64/include]
        end of search list found
        collapse include dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0] ==> [C:/msys64/mingw64/include/c++/15.1.0]
        collapse include dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/x86_64-w64-mingw32] ==> [C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32]
        collapse include dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/backward] ==> [C:/msys64/mingw64/include/c++/15.1.0/backward]
        collapse include dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        collapse include dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include] ==> [C:/msys64/mingw64/include]
        collapse include dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        collapse include dir [/mingw64/include] ==> [/mingw64/include]
        implicit include dirs: [C:/msys64/mingw64/include/c++/15.1.0;C:/msys64/mingw64/include/c++/15.1.0/x86_64-w64-mingw32;C:/msys64/mingw64/include/c++/15.1.0/backward;C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include;C:/msys64/mingw64/include;C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed;/mingw64/include]
      
      
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake:326 (find_library)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:251 (cmake_parse_implicit_link_info2)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    mode: "library"
    variable: "CMAKE_CXX_IMPLICIT_LINK_LIBRARY_stdc++"
    description: "Path to a library."
    settings:
      SearchFramework: "LAST"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "libstdc++.a"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/lib/gcc/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/x86_64-w64-mingw32/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/lib/gcc/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/x86_64-w64-mingw32/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/lib/"
      - "C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/"
      - "C:/msys64/mingw64/lib/gcc/"
      - "C:/msys64/mingw64/x86_64-w64-mingw32/lib/"
      - "C:/msys64/mingw64/lib/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/lib/gcc/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/lib/gcc/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/x86_64-w64-mingw32/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/x86_64-w64-mingw32/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/lib/gcc/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/lib/gcc/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/x86_64-w64-mingw32/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/x86_64-w64-mingw32/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/lib/"
      - "C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/"
      - "C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/"
      - "C:/msys64/mingw64/lib/gcc/"
      - "C:/msys64/mingw64/lib/gcc/"
      - "C:/msys64/mingw64/x86_64-w64-mingw32/lib/"
      - "C:/msys64/mingw64/x86_64-w64-mingw32/lib/"
    found: "C:/msys64/mingw64/lib/libstdc++.a"
    search_context:
      CMAKE_LIBRARY_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/dataset_export"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/msys64/mingw64"
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "C:/Program Files (x86)/dataset_export"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "C:/Program Files (x86)/dataset_export/bin"
        - "C:/msys64/mingw64/bin"
        - "/bin"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'C:/work/code/java/data-annotation-platform/native/build_static_opencv/CMakeFiles/CMakeScratch/TryCompile-7rg2kn']
        ignore line: []
        ignore line: [Run Build Command(s): C:/msys64/mingw64/bin/cmake.exe -E env VERBOSE=1 C:/msys64/mingw64/bin/mingw32-make.exe -f Makefile cmTC_11ea3/fast]
        ignore line: [C:/msys64/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_11ea3.dir\\build.make CMakeFiles/cmTC_11ea3.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'C:/work/code/java/data-annotation-platform/native/build_static_opencv/CMakeFiles/CMakeScratch/TryCompile-7rg2kn']
        ignore line: [Building CXX object CMakeFiles/cmTC_11ea3.dir/CMakeCXXCompilerABI.cpp.obj]
        ignore line: [C:\\msys64\\mingw64\\bin\\g++.exe   -static    -v -o CMakeFiles\\cmTC_11ea3.dir\\CMakeCXXCompilerABI.cpp.obj -c C:\\msys64\\mingw64\\share\\cmake\\Modules\\CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\msys64\\mingw64\\bin\\g++.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-15.1.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.1.0 (Rev8  Built by MSYS2 project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-static' '-v' '-o' 'CMakeFiles\\cmTC_11ea3.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_11ea3.dir\\']
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/cc1plus.exe -quiet -v -iprefix C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -D_REENTRANT C:\\msys64\\mingw64\\share\\cmake\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_11ea3.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=generic -march=nocona -version -o C:\\msys64\\tmp\\ccmzOlYf.s]
        ignore line: [GNU C++17 (Rev8  Built by MSYS2 project) version 15.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.1.0  GMP version 6.3.0  MPFR version 4.2.2  MPC version 1.3.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring nonexistent directory "C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0"]
        ignore line: [ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/x86_64-w64-mingw32"]
        ignore line: [ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/backward"]
        ignore line: [ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"]
        ignore line: [ignoring nonexistent directory "D:/M/msys64/mingw64/include"]
        ignore line: [ignoring duplicate directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"]
        ignore line: [ignoring nonexistent directory "C:/msys64/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "D:/M/msys64/mingw64/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0]
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/x86_64-w64-mingw32]
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include/c++/15.1.0/backward]
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include]
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        ignore line: [ /mingw64/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: 3b6fca90584e44f6819bdf9fe346ad35]
        ignore line: [COLLECT_GCC_OPTIONS='-static' '-v' '-o' 'CMakeFiles\\cmTC_11ea3.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_11ea3.dir\\']
        ignore line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_11ea3.dir\\CMakeCXXCompilerABI.cpp.obj C:\\msys64\\tmp\\ccmzOlYf.s]
        ignore line: [GNU assembler version 2.45 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.45]
        ignore line: [COMPILER_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-static' '-v' '-o' 'CMakeFiles\\cmTC_11ea3.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-mtune=generic' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_11ea3.dir\\CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_11ea3.exe]
        ignore line: [C:\\msys64\\mingw64\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_11ea3.dir\\link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=C:\\msys64\\mingw64\\bin\\g++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../gcc-15.1.0/configure --prefix=/mingw64 --with-local-prefix=/mingw64/local --with-native-system-header-dir=/mingw64/include --libexecdir=/mingw64/lib --enable-bootstrap --enable-checking=release --with-arch=nocona --with-tune=generic --enable-mingw-wildcard --enable-languages=c,lto,c++,fortran,ada,objc,obj-c++,jit --enable-shared --enable-static --enable-libatomic --enable-threads=posix --enable-graphite --enable-fully-dynamic-string --enable-libstdcxx-backtrace=yes --enable-libstdcxx-filesystem-ts --enable-libstdcxx-time --disable-libstdcxx-pch --enable-lto --enable-libgomp --disable-libssp --disable-multilib --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-libiconv --with-system-zlib --with-gmp=/mingw64 --with-mpfr=/mingw64 --with-mpc=/mingw64 --with-isl=/mingw64 --with-pkgversion='Rev8, Built by MSYS2 project' --with-bugurl=https://github.com/msys2/MINGW-packages/issues --with-gnu-as --with-gnu-ld --with-libstdcxx-zoneinfo=yes --disable-libstdcxx-debug --enable-plugin --with-boot-ldflags=-static-libstdc++ --with-stage1-ldflags=-static-libstdc++]
        ignore line: [Thread model: posix]
        ignore line: [Supported LTO compression algorithms: zlib zstd]
        ignore line: [gcc version 15.1.0 (Rev8  Built by MSYS2 project) ]
        ignore line: [COMPILER_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-static' '-static' '-v' '-o' 'cmTC_11ea3.exe' '-mtune=generic' '-march=nocona' '-dumpdir' 'cmTC_11ea3.']
        link line: [ C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\msys64\\tmp\\ccEeJD2q.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bstatic -o cmTC_11ea3.exe C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_11ea3.dir/objects.a --no-whole-archive --out-implib libcmTC_11ea3.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\msys64\\tmp\\ccEeJD2q.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bstatic] ==> search static
          arg [-o] ==> ignore
          arg [cmTC_11ea3.exe] ==> ignore
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o] ==> obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o]
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc] ==> dir [C:/msys64/mingw64/bin/../lib/gcc]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_11ea3.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_11ea3.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lstdc++] ==> lib [SEARCH_STATIC:stdc++]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lpthread] ==> lib [pthread]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o] ==> obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o]
          arg [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        ignore line: [collect2 version 15.1.0]
        ignore line: [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\msys64\\tmp\\ccEeJD2q.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -m i386pep -Bstatic -o cmTC_11ea3.exe C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LC:/msys64/mingw64/bin/../lib/gcc -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LC:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_11ea3.dir/objects.a --no-whole-archive --out-implib libcmTC_11ea3.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        linker tool for 'CXX': C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe
        search lib [SEARCH_STATIC:stdc++] ==> [C:/msys64/mingw64/lib/libstdc++.a]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        collapse obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/crt2.o] ==> [C:/msys64/mingw64/lib/crt2.o]
        collapse obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
        collapse obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/default-manifest.o] ==> [C:/msys64/mingw64/lib/default-manifest.o]
        collapse obj [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc] ==> [C:/msys64/mingw64/lib/gcc]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [C:/msys64/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> [C:/msys64/mingw64/lib]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> [C:/msys64/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [C:/msys64/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> [C:/msys64/mingw64/lib]
        implicit libs: [C:/msys64/mingw64/lib/libstdc++.a;mingw32;gcc;mingwex;kernel32;pthread;advapi32;shell32;user32;kernel32;mingw32;gcc;mingwex;kernel32]
        implicit objs: [C:/msys64/mingw64/lib/crt2.o;C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o;C:/msys64/mingw64/lib/default-manifest.o;C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        implicit dirs: [C:/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0;C:/msys64/mingw64/lib/gcc;C:/msys64/mingw64/x86_64-w64-mingw32/lib;C:/msys64/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/msys64/mingw64/x86_64-w64-mingw32/bin/ld.exe" "-v"
      GNU ld (GNU Binutils) 2.45
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/FindJNI.cmake:529 (find_library)"
      - "C:/msys64/home/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "CMakeLists.txt:35 (find_package)"
    mode: "library"
    variable: "JAVA_JVM_LIBRARY"
    description: "Java Virtual Machine library"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "jvm"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/client/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/minimal/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/server/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/zero/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/j9vm/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/default/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/client/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/minimal/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/server/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/zero/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/j9vm/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/default/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/client/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/minimal/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/server/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/zero/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/j9vm/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/default/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/client/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/minimal/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/server/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/zero/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/j9vm/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/default/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/mingw64/lib/"
      - "C:/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files (x86)/dataset_export/lib/"
      - "C:/Program Files (x86)/dataset_export/"
      - "C:/Program Files (x86)/dataset_export/bin/"
      - "/bin/"
      - "C:/java/jdk-21.0.2/lib/"
      - "C:/java/jdk-21.0.2/lib/client/"
      - "C:/java/jdk-21.0.2/lib/minimal/"
      - "C:/java/jdk-21.0.2/lib/server/"
      - "C:/java/jdk-21.0.2/lib/zero/"
      - "C:/java/jdk-21.0.2/lib/j9vm/"
      - "C:/java/jdk-21.0.2/lib/default/"
      - "C:/java/jdk-21.0.2/"
      - "C:/java/jdk-21.0.2/client/"
      - "C:/java/jdk-21.0.2/minimal/"
      - "C:/java/jdk-21.0.2/server/"
      - "C:/java/jdk-21.0.2/zero/"
      - "C:/java/jdk-21.0.2/j9vm/"
      - "C:/java/jdk-21.0.2/default/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/client/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/minimal/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/server/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/zero/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/j9vm/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/default/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/client/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/minimal/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/server/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/zero/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/j9vm/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/default/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/client/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/minimal/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/server/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/zero/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/j9vm/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/default/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/client/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/minimal/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/server/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/zero/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/j9vm/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/default/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/mingw64/lib/"
      - "C:/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files (x86)/dataset_export/lib/"
      - "C:/Program Files (x86)/dataset_export/"
      - "C:/Program Files (x86)/dataset_export/bin/"
      - "/bin/"
      - "C:/java/jdk-21.0.2/lib/"
      - "C:/java/jdk-21.0.2/lib/client/"
      - "C:/java/jdk-21.0.2/lib/minimal/"
      - "C:/java/jdk-21.0.2/lib/server/"
      - "C:/java/jdk-21.0.2/lib/zero/"
      - "C:/java/jdk-21.0.2/lib/j9vm/"
      - "C:/java/jdk-21.0.2/lib/default/"
      - "C:/java/jdk-21.0.2/"
      - "C:/java/jdk-21.0.2/client/"
      - "C:/java/jdk-21.0.2/minimal/"
      - "C:/java/jdk-21.0.2/server/"
      - "C:/java/jdk-21.0.2/zero/"
      - "C:/java/jdk-21.0.2/j9vm/"
      - "C:/java/jdk-21.0.2/default/"
    found: false
    search_context:
      CMAKE_LIBRARY_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
        - "C:/msys64/mingw64"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/dataset_export"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/msys64/mingw64"
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "C:/Program Files (x86)/dataset_export"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "C:/Program Files (x86)/dataset_export/bin"
        - "C:/msys64/mingw64/bin"
        - "/bin"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/FindJNI.cmake:535 (find_library)"
      - "C:/msys64/home/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "CMakeLists.txt:35 (find_package)"
    mode: "library"
    variable: "JAVA_AWT_LIBRARY"
    description: "Java AWT Native Interface library"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "jawt"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/mingw64/lib/"
      - "C:/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files (x86)/dataset_export/lib/"
      - "C:/Program Files (x86)/dataset_export/"
      - "C:/Program Files (x86)/dataset_export/bin/"
      - "/bin/"
      - "C:/java/jdk-21.0.2/lib/"
      - "C:/java/jdk-21.0.2/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/mingw64/lib/"
      - "C:/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
      - "C:/Program Files/lib/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/lib/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files (x86)/dataset_export/lib/"
      - "C:/Program Files (x86)/dataset_export/"
      - "C:/Program Files (x86)/dataset_export/bin/"
      - "/bin/"
      - "C:/java/jdk-21.0.2/lib/"
      - "C:/java/jdk-21.0.2/"
    found: false
    search_context:
      CMAKE_LIBRARY_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/manual-link"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/lib/manual-link"
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
        - "C:/msys64/mingw64"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/dataset_export"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/msys64/mingw64"
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "C:/Program Files (x86)/dataset_export"
      CMAKE_SYSTEM_LIBRARY_PATH:
        - "C:/Program Files (x86)/dataset_export/bin"
        - "C:/msys64/mingw64/bin"
        - "/bin"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/FindJNI.cmake:557 (find_path)"
      - "C:/msys64/home/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "CMakeLists.txt:35 (find_package)"
    mode: "path"
    variable: "JAVA_INCLUDE_PATH"
    description: "JNI include directory"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "jni.h"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/mingw64/include/"
      - "C:/msys64/mingw64/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
      - "C:/Program Files/include/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/include/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files (x86)/dataset_export/include/"
      - "C:/Program Files (x86)/dataset_export/"
      - "C:/java/jdk-21.0.2/include/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/jni.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/jni.h"
      - "C:/msys64/mingw64/include/jni.h"
      - "C:/msys64/mingw64/jni.h"
      - "C:/msys64/mingw64/bin/jni.h"
      - "C:/msys64/usr/local/bin/jni.h"
      - "C:/msys64/usr/bin/jni.h"
      - "C:/Windows/System32/jni.h"
      - "C:/Windows/jni.h"
      - "C:/Windows/System32/wbem/jni.h"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/jni.h"
      - "C:/msys64/usr/bin/site_perl/jni.h"
      - "C:/msys64/usr/bin/vendor_perl/jni.h"
      - "C:/msys64/usr/bin/core_perl/jni.h"
      - "C:/Program Files/include/jni.h"
      - "C:/Program Files/jni.h"
      - "C:/Program Files (x86)/include/jni.h"
      - "C:/Program Files (x86)/jni.h"
      - "C:/Program Files (x86)/dataset_export/include/jni.h"
      - "C:/Program Files (x86)/dataset_export/jni.h"
    found: "C:/java/jdk-21.0.2/include/"
    search_context:
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
        - "C:/msys64/mingw64"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/dataset_export"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/msys64/mingw64"
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "C:/Program Files (x86)/dataset_export"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/msys64/mingw64/share/cmake/Modules/CheckSourceCompiles.cmake:205 (cmake_check_source_compiles)"
      - "C:/msys64/mingw64/share/cmake/Modules/FindJNI.cmake:581 (check_source_compiles)"
      - "C:/msys64/home/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "CMakeLists.txt:35 (find_package)"
    directories:
      source: "C:/work/code/java/data-annotation-platform/native/build_static_opencv/CMakeFiles/CMakeScratch/TryCompile-8f88nj"
      binary: "C:/work/code/java/data-annotation-platform/native/build_static_opencv/CMakeFiles/CMakeScratch/TryCompile-8f88nj"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-static"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-static -static"
      VCPKG_INSTALLED_DIR: "C:/msys64/home/<USER>/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-mingw-static"
      Z_VCPKG_ROOT_DIR: "C:/msys64/home/<USER>/vcpkg"
    buildResult:
      variable: "JNI_INCLUDE_PATH2_OPTIONAL"
      cached: true
      stdout: |
        Change Dir: 'C:/work/code/java/data-annotation-platform/native/build_static_opencv/CMakeFiles/CMakeScratch/TryCompile-8f88nj'
        
        Run Build Command(s): C:/msys64/mingw64/bin/cmake.exe -E env VERBOSE=1 C:/msys64/mingw64/bin/mingw32-make.exe -f Makefile cmTC_f3669/fast
        C:/msys64/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_f3669.dir\\build.make CMakeFiles/cmTC_f3669.dir/build
        mingw32-make[1]: Entering directory 'C:/work/code/java/data-annotation-platform/native/build_static_opencv/CMakeFiles/CMakeScratch/TryCompile-8f88nj'
        Building CXX object CMakeFiles/cmTC_f3669.dir/src.cxx.obj
        C:\\msys64\\mingw64\\bin\\g++.exe -DJNI_INCLUDE_PATH2_OPTIONAL @CMakeFiles/cmTC_f3669.dir/includes_CXX.rsp -static  -std=gnu++17 -o CMakeFiles\\cmTC_f3669.dir\\src.cxx.obj -c C:\\work\\code\\java\\data-annotation-platform\\native\\build_static_opencv\\CMakeFiles\\CMakeScratch\\TryCompile-8f88nj\\src.cxx
        In file included from C:\\work\\code\\java\\data-annotation-platform\\native\\build_static_opencv\\CMakeFiles\\CMakeScratch\\TryCompile-8f88nj\\src.cxx:2:
        C:/java/jdk-21.0.2/include/jni.h:45:10: fatal error: jni_md.h: No such file or directory
           45 | #include "jni_md.h"
              |          ^~~~~~~~~~
        compilation terminated.
        mingw32-make[1]: *** [CMakeFiles\\cmTC_f3669.dir\\build.make:81: CMakeFiles/cmTC_f3669.dir/src.cxx.obj] Error 1
        mingw32-make[1]: Leaving directory 'C:/work/code/java/data-annotation-platform/native/build_static_opencv/CMakeFiles/CMakeScratch/TryCompile-8f88nj'
        mingw32-make: *** [Makefile:133: cmTC_f3669/fast] Error 2
        
      exitCode: 2
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/FindJNI.cmake:597 (find_path)"
      - "C:/msys64/home/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "CMakeLists.txt:35 (find_package)"
    mode: "path"
    variable: "JAVA_INCLUDE_PATH2"
    description: "jni_md.h jniport.h include directory"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "jni_md.h"
      - "jniport.h"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/darwin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/win32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/linux/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/freebsd/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/openbsd/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/solaris/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/hp-ux/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/alpha/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/aix/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/darwin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/win32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/linux/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/freebsd/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/openbsd/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/solaris/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/hp-ux/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/alpha/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/aix/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/mingw64/include/"
      - "C:/msys64/mingw64/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
      - "C:/Program Files/include/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/include/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files (x86)/dataset_export/include/"
      - "C:/Program Files (x86)/dataset_export/"
      - "C:/java/jdk-21.0.2/include/"
      - "C:/java/jdk-21.0.2/include/darwin/"
      - "C:/java/jdk-21.0.2/include/win32/"
      - "C:/java/jdk-21.0.2/include/linux/"
      - "C:/java/jdk-21.0.2/include/freebsd/"
      - "C:/java/jdk-21.0.2/include/openbsd/"
      - "C:/java/jdk-21.0.2/include/solaris/"
      - "C:/java/jdk-21.0.2/include/hp-ux/"
      - "C:/java/jdk-21.0.2/include/alpha/"
      - "C:/java/jdk-21.0.2/include/aix/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/darwin/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/win32/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/linux/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/freebsd/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/openbsd/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/solaris/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/hp-ux/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/alpha/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/aix/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/darwin/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/win32/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/linux/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/freebsd/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/openbsd/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/solaris/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/hp-ux/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/alpha/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/aix/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/jni_md.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/jni_md.h"
      - "C:/msys64/mingw64/include/jni_md.h"
      - "C:/msys64/mingw64/jni_md.h"
      - "C:/msys64/mingw64/bin/jni_md.h"
      - "C:/msys64/usr/local/bin/jni_md.h"
      - "C:/msys64/usr/bin/jni_md.h"
      - "C:/Windows/System32/jni_md.h"
      - "C:/Windows/jni_md.h"
      - "C:/Windows/System32/wbem/jni_md.h"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/jni_md.h"
      - "C:/msys64/usr/bin/site_perl/jni_md.h"
      - "C:/msys64/usr/bin/vendor_perl/jni_md.h"
      - "C:/msys64/usr/bin/core_perl/jni_md.h"
      - "C:/Program Files/include/jni_md.h"
      - "C:/Program Files/jni_md.h"
      - "C:/Program Files (x86)/include/jni_md.h"
      - "C:/Program Files (x86)/jni_md.h"
      - "C:/Program Files (x86)/dataset_export/include/jni_md.h"
      - "C:/Program Files (x86)/dataset_export/jni_md.h"
      - "C:/java/jdk-21.0.2/include/jni_md.h"
      - "C:/java/jdk-21.0.2/include/darwin/jni_md.h"
    found: "C:/java/jdk-21.0.2/include/win32/"
    search_context:
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
        - "C:/msys64/mingw64"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/dataset_export"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/msys64/mingw64"
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "C:/Program Files (x86)/dataset_export"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
  -
    kind: "find-v1"
    backtrace:
      - "C:/msys64/mingw64/share/cmake/Modules/FindJNI.cmake:612 (find_path)"
      - "C:/msys64/home/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "CMakeLists.txt:35 (find_package)"
    mode: "path"
    variable: "JAVA_AWT_INCLUDE_PATH"
    description: "Java AWT Native Interface include directory"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "LAST"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "jawt.h"
    candidate_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/"
      - "C:/msys64/mingw64/include/"
      - "C:/msys64/mingw64/"
      - "C:/msys64/mingw64/bin/"
      - "C:/msys64/usr/local/bin/"
      - "C:/msys64/usr/bin/"
      - "C:/Windows/System32/"
      - "C:/Windows/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "C:/msys64/usr/bin/site_perl/"
      - "C:/msys64/usr/bin/vendor_perl/"
      - "C:/msys64/usr/bin/core_perl/"
      - "C:/Program Files/include/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/include/"
      - "C:/Program Files (x86)/"
      - "C:/Program Files (x86)/dataset_export/include/"
      - "C:/Program Files (x86)/dataset_export/"
      - "C:/java/jdk-21.0.2/include/"
    searched_directories:
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/mingw64/bin/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/local/bin/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/wbem/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Windows/System32/WindowsPowerShell/v1.0/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/site_perl/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/vendor_perl/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/msys64/usr/bin/core_perl/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/Program Files (x86)/dataset_export/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/java/jdk-21.0.2/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/mingw64/bin/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/local/bin/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/wbem/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Windows/System32/WindowsPowerShell/v1.0/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/site_perl/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/vendor_perl/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/msys64/usr/bin/core_perl/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/Program Files (x86)/dataset_export/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/java/jdk-21.0.2/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/include/jawt.h"
      - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug/jawt.h"
      - "C:/msys64/mingw64/include/jawt.h"
      - "C:/msys64/mingw64/jawt.h"
      - "C:/msys64/mingw64/bin/jawt.h"
      - "C:/msys64/usr/local/bin/jawt.h"
      - "C:/msys64/usr/bin/jawt.h"
      - "C:/Windows/System32/jawt.h"
      - "C:/Windows/jawt.h"
      - "C:/Windows/System32/wbem/jawt.h"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/jawt.h"
      - "C:/msys64/usr/bin/site_perl/jawt.h"
      - "C:/msys64/usr/bin/vendor_perl/jawt.h"
      - "C:/msys64/usr/bin/core_perl/jawt.h"
      - "C:/Program Files/include/jawt.h"
      - "C:/Program Files/jawt.h"
      - "C:/Program Files (x86)/include/jawt.h"
      - "C:/Program Files (x86)/jawt.h"
      - "C:/Program Files (x86)/dataset_export/include/jawt.h"
      - "C:/Program Files (x86)/dataset_export/jawt.h"
    found: "C:/java/jdk-21.0.2/include/"
    search_context:
      CMAKE_PREFIX_PATH:
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static"
        - "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
        - "C:/msys64/mingw64"
      ENV{PATH}:
        - "C:\\msys64\\mingw64\\bin"
        - "C:\\msys64\\usr\\local\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\msys64\\usr\\bin"
        - "C:\\Windows\\System32"
        - "C:\\Windows"
        - "C:\\Windows\\System32\\Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\"
        - "C:\\msys64\\usr\\bin\\site_perl"
        - "C:\\msys64\\usr\\bin\\vendor_perl"
        - "C:\\msys64\\usr\\bin\\core_perl"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/dataset_export"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "C:/msys64/mingw64"
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "C:/msys64/mingw64"
        - "C:/Program Files (x86)/dataset_export"
      CMAKE_FIND_ROOT_PATH: "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static;C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/debug"
...
