#!/bin/bash

# 使用用户编译的静态OpenCV进行完全静态编译
# 这个脚本会使用您在 C:\dev\opencv_static 编译的静态OpenCV库

echo "========================================"
echo "使用静态OpenCV进行完全静态编译"
echo "========================================"

# 检查MSYS2环境
if [ -z "$MSYSTEM" ]; then
    echo "❌ 请在MSYS2环境中运行此脚本"
    exit 1
fi

echo "✓ MSYS2环境: $MSYSTEM"

# 检查用户编译的静态OpenCV
OPENCV_STATIC_ROOT="C:/dev/opencv_static"
OPENCV_STATIC_LIB_DIR="$OPENCV_STATIC_ROOT/x64/mingw/staticlib"

echo "========================================"
echo "检查静态OpenCV库"
echo "========================================"

if [ ! -d "$OPENCV_STATIC_ROOT" ]; then
    echo "❌ 未找到静态OpenCV目录: $OPENCV_STATIC_ROOT"
    echo "请确保您已经按照教程编译了静态版本的OpenCV"
    exit 1
fi

if [ ! -d "$OPENCV_STATIC_LIB_DIR" ]; then
    echo "❌ 未找到静态库目录: $OPENCV_STATIC_LIB_DIR"
    echo "请检查OpenCV编译输出目录结构"
    exit 1
fi

echo "✓ 找到静态OpenCV根目录: $OPENCV_STATIC_ROOT"
echo "✓ 找到静态库目录: $OPENCV_STATIC_LIB_DIR"

# 检查关键库文件
echo ""
echo "检查关键OpenCV库文件:"
REQUIRED_LIBS=("opencv_core" "opencv_imgproc" "opencv_imgcodecs")
ALL_FOUND=true

for lib in "${REQUIRED_LIBS[@]}"; do
    if ls "$OPENCV_STATIC_LIB_DIR"/lib${lib}*.a >/dev/null 2>&1; then
        lib_file=$(ls "$OPENCV_STATIC_LIB_DIR"/lib${lib}*.a | head -1)
        echo "✓ 找到: $(basename "$lib_file")"
    else
        echo "❌ 未找到: lib${lib}*.a"
        ALL_FOUND=false
    fi
done

if [ "$ALL_FOUND" = false ]; then
    echo ""
    echo "❌ 缺少必要的OpenCV库文件"
    echo "请检查OpenCV编译是否成功完成"
    exit 1
fi

echo ""
echo "✅ 所有必要的OpenCV静态库都已找到"

# 检查系统依赖
echo "========================================"
echo "检查系统依赖"
echo "========================================"

REQUIRED_PACKAGES=(
    "mingw-w64-x86_64-gcc"
    "mingw-w64-x86_64-cmake" 
    "mingw-w64-x86_64-make"
    "mingw-w64-x86_64-nlohmann-json"
)

for pkg in "${REQUIRED_PACKAGES[@]}"; do
    if pacman -Q "$pkg" >/dev/null 2>&1; then
        echo "✓ $pkg 已安装"
    else
        echo "❌ $pkg 未安装"
        echo "尝试安装 $pkg..."
        pacman -S --noconfirm "$pkg"
    fi
done

# 检查vcpkg（用于MinIO）- (此部分保持不变)
# ...

# 创建并进入构建目录
echo "========================================"
echo "配置构建环境"
echo "========================================"

BUILD_DIR="build" # 使用通用的 build 目录名
rm -rf "$BUILD_DIR"
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

echo "✓ 已创建并进入构建目录: $BUILD_DIR"

# 设置环境变量
export CC=/mingw64/bin/gcc.exe
export CXX=/mingw64/bin/g++.exe

echo "✓ 设置编译器: $CC, $CXX"

# ==================== 关键修改点 ====================
# CMake配置参数
# 我们移除了所有 -DCMAKE_*_FLAGS 和 -DCMAKE_*_LINKER_FLAGS
# 因为这些都由CMakeLists.txt内部更安全地处理了。
# 这样可以避免在配置阶段因-static标志导致测试编译失败。
CMAKE_ARGS=(
    -G "MinGW Makefiles"
    -DCMAKE_BUILD_TYPE=Release
    -DBUILD_STATIC=ON # 这个开关会告诉CMakeLists.txt启用所有静态链接逻辑
    # 编译器路径让CMake自动查找即可，除非有特殊需要
    # -DCMAKE_C_COMPILER="$CC"
    # -DCMAKE_CXX_COMPILER="$CXX"
    -DCMAKE_MAKE_PROGRAM=/mingw64/bin/mingw32-make.exe
)
# ====================================================

# 如果有vcpkg，添加vcpkg支持 (此部分保持不变)
# ...

# 运行CMake配置
echo "========================================"
echo "运行CMake配置"
echo "========================================"

echo "CMake参数:"
printf "  %s\n" "${CMAKE_ARGS[@]}"

echo ""
echo "开始配置..."

cmake .. "${CMAKE_ARGS[@]}"

if [ $? -ne 0 ]; then
    echo "❌ CMake配置失败！"
    echo ""
    echo "可能的原因："
    echo "1. CMakeLists.txt有语法错误"
    echo "2. 依赖库的路径设置不正确（如OpenCV_DIR）"
    echo ""
    echo "请检查上面的错误信息"
    exit 1
fi

echo "✅ CMake配置成功！"

# 开始编译
echo "========================================"
echo "开始编译"
echo "========================================"

echo "使用 $(nproc) 个并行任务进行编译..."

cmake --build . -- -j$(nproc)

if [ $? -ne 0 ]; then
    echo "❌ 编译失败！"
    echo ""
    echo "可能的原因："
    echo "1. 静态库链接错误（依赖缺失或顺序错误）"
    echo "2. 源代码有错误"
    echo "3. 内存不足"
    echo ""
    echo "请检查上面的错误信息"
    exit 1
fi

echo "✅ 编译成功！"

# 检查生成的DLL
echo "========================================"
echo "检查编译结果"
echo "========================================"

DLL_FILE=""
# CMake的输出目录是确定的
if [ -f "bin/libdataset_export.dll" ]; then
    DLL_FILE="bin/libdataset_export.dll"
elif [ -f "bin/dataset_export.dll" ]; then
    DLL_FILE="bin/dataset_export.dll"
fi
if [ -z "$DLL_FILE" ]; then
    echo "❌ 未找到生成的DLL文件 (预期在 bin/libdataset_export.dll 或 bin/dataset_export.dll)"
    find . -name "*.dll" # 打印出实际生成了什么，方便调试
    exit 1
fi

echo "✓ 找到生成的DLL: $DLL_FILE"
echo "文件大小: $(ls -lh "$DLL_FILE" | awk '{print $5}')"

# 检查依赖关系
echo ""
echo "检查DLL的静态链接情况:"
NON_SYSTEM_DEPS=$(objdump -p "$DLL_FILE" | grep "DLL Name:" | grep -v -E "(KERNEL32|USER32|GDI32|ADVAPI32|msvcrt|WS2_32|WINMM|CRYPT32|BCRYPT|Secur32|IPHLPAPI|SHELL32|COMDLG32|OPENGL32|OLE32|OLEAUT32|UUID)\.dll")

if [ -n "$NON_SYSTEM_DEPS" ]; then
    echo "⚠️  仍然发现非系统依赖！"
    echo "这意味着不是完全静态编译，可能无法在其他电脑上直接运行。"
    echo "发现的依赖："
    echo "$NON_SYSTEM_DEPS"
else
    echo "🎉 恭喜！这是真正的完全静态编译！"
    echo "只依赖Windows系统核心DLL，可以直接复制到其他机器使用。"
fi

# 复制到上级目录，并重命名以示区别
echo ""
echo "复制DLL到项目根目录..."
cp "$DLL_FILE" "../dataset_export-static.dll"
echo "✓ 已复制并重命名为 ../dataset_export-static.dll"

# 显示总结
echo ""
echo "========================================"
echo "编译完成总结"
echo "========================================"
echo "✅ 使用静态OpenCV编译成功！"
echo "📁 DLL位置: $(pwd)/$DLL_FILE"
echo "📁 最终产物: ../dataset_export-static.dll"
echo "🔧 使用的OpenCV: $OPENCV_STATIC_ROOT"
echo "💾 文件大小: $(ls -lh "$DLL_FILE" | awk '{print $5}')"

echo ""
echo "🎉 完全静态编译完成！"
